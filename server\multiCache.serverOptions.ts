import { defineMultiCacheOptions } from 'nuxt-multi-cache/dist/runtime/serverOptions'
import redis from 'unstorage/drivers/redis'

export default defineMultiCacheOptions({
	api: {
		authorization: () => {
			return Promise.resolve(true)
		},
	},
	route: {
		applies(_path) {
			if (process.env.ENABLE_CACHE !== 'true') {
				return false
			}

			return true
		},
		storage: {
			driver: redis({
				tls: {
					host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
					port: 6379,
				},
			}),
		},
	},

	data: {
		storage: {
			driver: redis({
				tls: {
					host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
					port: 6379,
				},
			}),
		},
	},
})
