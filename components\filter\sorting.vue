<script lang="ts" setup>
import { useMediaQuery } from '@vueuse/core'
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
	DropdownMenuCheckboxItem,
} from '~/components/ui/dropdown-menu'

interface CategoryProps {
	total?: string | number
	update?: Function
	loading?: boolean
}
const isDesktop = useMediaQuery('(min-width: 600px)', { ssrWidth: 1000 })
const { update, total = 0, loading = false } = defineProps<CategoryProps>()
const route = useRoute()
const list = ref([
	{
		key: 'createdAt,desc',
		title: 'filters.soring-desc-title',
		selected: true,
	}, {
		key: 'createdAt,asc',
		title: 'filters.soring-asc-title',
		selected: false,
	}, {
		key: 'avgRate,desc',
		title: 'filters.soring-most-rate-title',
		selected: false,
	}, {
		key: 'avgRate,asc',
		title: 'filters.soring-less-rate-title',
		selected: false,
	}, {
		key: 'basePrice,desc',
		title: 'filters.soring-most-price-title',
		selected: false,
	}, {
		key: 'basePrice,asc',
		title: 'filters.soring-less-price-title',
		selected: false,
	},
])

const selected = computed(() => list.value.find(i => !!i.selected))

/**
 * Handle on select order by key
 * @param key
 * @param isUpdate
 */
const onSelect = (key, isUpdate = true) => {
	if (!key) {
		return
	}

	list.value = list.value.map((item) => {
		return {
			...item,
			selected: key == item.key,
		}
	})

	if (isUpdate) {
		update({ orderBy: key })
	}
}

onMounted(() => {
	onSelect(route.query?.orderBy, false)
})
</script>

<template>
	<div class="flex flex-col w-full">
		<div
			v-if="isDesktop"
			class="flex gap-2 px-2 items-center xs:hidden sm:flex"
		>
			<template v-if="loading">
				<Skeleton class="w-44 h-8" />
				<Skeleton class="w-16 h-8" />
			</template>
			<template v-else>
				<div class="border border-gray-200 rounded-lg p-2">
					<DropdownMenu>
						<DropdownMenuTrigger class="flex gap-1 text-xs focus-within:border-primary-600 active:border-primary-600">
							<Icon
								name="lucide:list-filter"
								size="17px"
							/>
							<span>
								{{ $t("filters.sorting-title") }}
							</span>
							<span class="ps-1 text-primary-600">
								{{ $t(selected.title) }}
							</span>
						</DropdownMenuTrigger>
						<DropdownMenuContent class="w-48">
							<DropdownMenuCheckboxItem
								v-for="item in list"
								:key="item.key"
								v-model:model-value="item.selected"
								class="text-start w-full text-xs text-gray-500 py-2 px-4 cursor-pointer hover:bg-primary-100 font-medium"
								:class="{ 'pointer-events-none': item.selected }"
								@close-auto-focus="true"
								@select="onSelect(item.key)"
							>
								<div class="flex items-center">
									<span :class="{ 'text-primary-500': item.selected }">{{ $t(item.title) }}</span>
								</div>
							</DropdownMenuCheckboxItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>

				<span class="text-sm font-normal">{{ total }} {{ $t('filters.result-title') }}</span>
			</template>
		</div>

		<div
			v-else-if="!isDesktop"
			class="flex flex-col px-2 items-center"
		>
			<div
				v-for="item in list"
				:key="item.key"
				class="text-start w-full text-xs text-gray-500 py-2 px-4 cursor-pointer hover:bg-primary-100 font-medium"
				:class="{ 'pointer-events-none': item.selected }"
				@click="onSelect(item.key)"
			>
				<div
					class="flex items-center justify-between text-base font-medium"
				>
					<span :class="{ 'text-primary-500': item.selected }">{{ $t(item.title) }}</span>
					<div>
						<Icon
							v-if="item.selected"
							name="lucide:check"
							class="w-6 h-6 text-primary-600"
						/>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
