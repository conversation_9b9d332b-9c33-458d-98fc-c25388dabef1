import type { UseHeadInput } from '@unhead/vue'
import type { Details } from '~/interfaces/product/details'
import type { Rate, Review } from '~/interfaces/product/rate'

export const ProductSEO = (product: Details, rate: Rate, reviews: Review[]) => {
	const route = useRoute()
	const { t } = useI18n()
	const config = useRuntimeConfig()
	const localePath = useLocalePath()

	const productUrl = computed(() => `${config.public.siteUrl}${localePath(route.path)}`)
	const hasVariant = computed(() => route.params?.slug?.length > 1)

	const metaTitle = computed(() => {
		if (!hasVariant.value) {
			return `${product?.metaTitle} | ${t('header.meta-site-name')}`
		}

		return `${product?.variance?.metaTitle || ''} ${product?.metaTitle || ''}  | ${t('header.meta-site-name')}`
	})

	const metaDescription = computed(() => {
		if (!hasVariant.value) {
			return `${product?.metaDescription}`
		}

		return `${product?.variance?.metaDescription || ''} ${product?.metaTitle || ''}`
	})

	const reviewList = computed(() => reviews?.map((review) => {
		return ({
			aggregateRating: {
				'@type': 'AggregateRating',
				'ratingValue': review?.rating,
				'reviewCount': review?.review?.length,
			},
			review: {
				'id': review?.userId,
				'@type': 'Review',
				'author': {
					'@type': 'Person',
					'name': `${review.user?.firstName || ''} ${review.user?.lastName || ''}`,
				},
				'datePublished': '2024-11-20',
				'reviewRating': {
					'@type': 'Rating',
					'ratingValue': review?.rating,
					'bestRating': '5',
				},
				'reviewBody': review?.review,
			},
		})
	}))

	const variants = computed(() => {
		return product.variances.map(variant => ({
			'@type': 'Product',
			'sku': product?.SKU,
			'image': product?.media?.gallery?.[0]?.src,
			'name': variant?.metaTitle,
			'description': variant?.metaDescription,
			...(variant.stock?.priceBeforeOffer
				? {
						offers: {
							'@type': 'Offer',
							'url': productUrl.value,
							'priceCurrency': 'JOD',
							'price': `${variant?.stock?.price}`,
							'priceValidUntil': variant?.stock?.unPublishedAt ?? '',
							'itemCondition': 'https://schema.org/NewCondition',
							'availability': 'https://schema.org/InStock',
							'shippingDetails': { '@id': 'https://action.jo/en/shipping-details' },
							'hasMerchantReturnPolicy': { '@id': 'https://action.jo/en/privacy' },
						},
					}
				: {}),
			...(reviewList.value ? { review: reviewList.value } : {}),
		}))
	})

	const productGroupSchema = [
		{
			'@context': 'https://schema.org/',
			'@type': 'ProductGroup',
			'name': metaTitle.value,
			'description': metaDescription.value,
			'url': productUrl.value,
			'brand': {
				'@type': 'brand',
				'name': product.brand,
			},
			'productGroupID': product?.productId,
			'variesBy': [
				'https://schema.org/size',
				'https://schema.org/color',
			],
			...(reviewList.value ? { review: [...reviewList.value] } : {}),
			...(product.variances.length ? { hasVariant: variants.value } : {}),
		},
		{
			'@context': 'https://schema.org/',
			'@type': 'OfferShippingDetails',
			'@id': 'https://action.jo/en/page/shipping-details',
			'shippingRate': {
				'@type': 'MonetaryAmount',
				'value': `${product?.variance?.stock?.price}`,
				'currency': 'JOD',
			},
			'shippingDestination': {
				'@type': 'DefinedRegion',
				'addressCountry': 'JO',
			},
			'deliveryTime': {
				'@type': 'ShippingDeliveryTime',
				'handlingTime': {
					'@type': 'QuantitativeValue',
					'minValue': 0,
					'maxValue': product?.variance?.stock.maxPerUser,
					'unitCode': 'DAY',
				},
				'transitTime': {
					'@type': 'QuantitativeValue',
					'minValue': 1,
					'maxValue': product?.variance?.stock.maxPerUser,
					'unitCode': 'DAY',
				},
			},
		},
		{
			'@context': 'https://schema.org/',
			'@type': 'MerchantReturnPolicy',
			'@id': 'https://action.jo/en/privacy',
			'applicableCountry': 'JO',
			'returnPolicyCountry': 'JO',
			'merchantReturnDays': 3,
			'returnPolicyCategory': 'https://schema.org/MerchantReturnFiniteReturnWindow',
			'returnMethod': 'https://schema.org/ReturnByMail',
			'returnFees': 'https://schema.org/FreeReturn',
		},
	]

	let script = {
		script: [
			{
				type: 'application/ld+json',
				innerHTML: JSON.stringify({
					'@context': 'https://schema.org',
					'@type': 'LocalBusiness',
					'name': t('app.action-page-title'),
					'image': 'https://action.jo/images/logo.png',
					'@id': productUrl.value,
					'url': productUrl.value,
					'telephone': '962791009595',
					'priceRange': '10',
					'address': {
						'@type': 'PostalAddress',
						'streetAddress': '329 K. Abdullah I St.',
						'addressLocality': 'Amman',
						'postalCode': '11185',
						'addressCountry': 'JO',
					},
					'geo': {
						'@type': 'GeoCoordinates',
						'latitude': 31.9785307,
						'longitude': 35.9728592,
					},
					'openingHoursSpecification': {
						'@type': 'OpeningHoursSpecification',
						'dayOfWeek': [
							'Monday',
							'Tuesday',
							'Wednesday',
							'Thursday',
							'Friday',
							'Saturday',
							'Sunday',
						],
						'opens': '10:00',
						'closes': '23:00',
					},
					'sameAs': [
						'https://www.facebook.com/Actionmobile11',
						'https://www.instagram.com/actionwebsite/',
						productUrl.value,
					],
				}),
				tagPosition: 'head', // Ensure it's in the <head>
			},
		],
		// @ts-ignore
		__dangerouslyDisableSanitizersByTagID: {
			// Disable sanitization for this script
			'ld-json-schema': ['innerHTML'],
		},
	}

	// if master product and has variance
	if (!!product.variances.length && route.params?.slug?.length <= 1)
		script = {
			script: [
				{
					type: 'application/ld+json',
					// @ts-ignore
					id: 'ld-json-product-group',
					innerHTML: JSON.stringify(productGroupSchema),
					tagPosition: 'head',
				},
			],
			// @ts-ignore
			__dangerouslyDisableSanitizersByTagID: {
				// @ts-ignore
				'ld-json-product-group': ['innerHTML'],
			},
		}

	useHead(script as UseHeadInput)
}
