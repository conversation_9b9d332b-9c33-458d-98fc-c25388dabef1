/**
 * Composable for the Infinite Scroll Api
 * @param targetRef
 * @param callback
 * @param rootMargin
 * @param isIntersecting
 */
export function useInfiniteScroll(targetRef: Ref, callback: Function, rootMargin: string = '100px', isIntersecting = true) {
	const observer = ref<IntersectionObserver | null>(null)

	onMounted(() => {
		if (!targetRef.value) return

		observer.value = new IntersectionObserver(
			async ([entry]) => {
				if (entry.isIntersecting && isIntersecting) {
					await callback(entry) // Execute the provided callback function
				}

				if (!isIntersecting) {
					await callback(entry)
				}
			},
			{ rootMargin, threshold: 0.01 },
		)

		observer.value.observe(targetRef.value)
	})

	onUnmounted(() => {
		observer.value?.disconnect()
	})

	watchEffect(() => {
		if (targetRef.value && observer.value) {
			observer.value.observe(targetRef.value)
		}
	})
}
