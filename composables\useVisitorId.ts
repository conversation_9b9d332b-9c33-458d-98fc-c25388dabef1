export const useVisitorId = () => {
	const visitorId = useCookie<string>('visitorId', {
		maxAge: 60 * 60 * 24 * 365, // set for 1 year
		sameSite: 'lax',
		path: '/',
		secure: true,
	})

	const generateVisitorId = () => {
		const timestamp = new Date().getTime().toString()
		let id = ''

		for (let i = 0; i < timestamp.length; i++) {
			const digit = timestamp.charAt(i)

			if (digit) {
				id += digit
			}
		}

		return id
	}

	const setVisitorId = () => {
		// Only set if it is not already set
		if (!visitorId.value) {
			visitorId.value = generateVisitorId()
		}

		console.log('Visitor Id: ', visitorId.value)
	}

	return {
		generateVisitorId,
		setVisitorId,
		visitorId,
	}
}
