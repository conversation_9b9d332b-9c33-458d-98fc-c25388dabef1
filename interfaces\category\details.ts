export interface CategoryDetails {
	brands?: Brand[]
	children?: CategoryDetails[]
	metaTitle?: string
	name?: string
	media?: DetailsMedia
	metaDescription?: string
	isShowBrandIngListing?: boolean
	categoryId?: number
	slug?: string
	parentId?: number
}

export interface Brand {
	brandId: number
	metaTitle: string
	name: string
	media: BrandMedia
	metaDescription: string
	slug: string
}

export interface BrandMedia {
	logoName: Cover[]
	logo: Cover[]
}

export interface Cover {
	preview: string
	disk: Disk
	src: string
	fileSize: number
	id: number
	mimeType: MIMEType
	sort: number
}

export enum Disk {
	S3 = 's3',
}

export enum MIMEType {
	ImagePNG = 'image/png',
	ImageWebp = 'image/webp',
}

export interface DetailsMedia {
	cover: Cover
}
