<script lang="ts" setup>
import { useMediaQuery, createReusableTemplate } from '@vueuse/core'

const isDesktop = useMediaQuery('(min-width: 600px)')
const [UseBody, BodyGrid] = createReusableTemplate()
const [UseFooter, FooterGrid] = createReusableTemplate()
const { title, description, dismissible, size = 'max-w-lg' } = defineProps<{
	title?: string
	description?: string
	dismissible?: boolean
	hideClose?: boolean
	size?: string | 'max-w-2xl' | 'max-w-xl' | 'max-w-lg' | 'max-w-md' | 'max-w-sm' | 'max-w-xs'
}>()

const emit = defineEmits<{
	(event: 'close'): void
}>()
</script>

<template>
	<UseBody>
		<slot name="body" />
	</UseBody>

	<UseFooter>
		<slot name="footer" />
	</UseFooter>

	<template v-if="isDesktop">
		<Dialog
			default-open
			@update:open="emit('close')"
		>
			<DialogContent
				:class="[size]"
				:hide-close="hideClose"
			>
				<DialogHeader :class="{ hidden: !title }">
					<DialogTitle>
						{{ title || 'Dialog title' }}
					</DialogTitle>
				</DialogHeader>
				<DialogDescription :class="{ hidden: !description }">
					{{ description || 'Dialog description' }}
				</DialogDescription>
				<div class="max-h-[65vh] min-h-24 h-full overflow-y-auto">
					<BodyGrid />
				</div>

				<DialogFooter>
					<FooterGrid />
				</DialogFooter>
			</DialogContent>
		</Dialog>
	</template>
	<template v-if="!isDesktop">
		<Drawer
			:default-open="true"
			:dismissible="dismissible"
		>
			<DrawerContent @close="emit('close')">
				<DrawerHeader
					v-if="title"
					class="border-b border-gray-200"
				>
					<DrawerTitle>
						{{ title }}
					</DrawerTitle>
				</DrawerHeader>
				<DrawerDescription class="p-4">
					{{ description }}
				</DrawerDescription>

				<BodyGrid />

				<DrawerFooter class="pt-2">
					<FooterGrid />
				</DrawerFooter>
			</DrawerContent>
		</Drawer>
	</template>
</template>
