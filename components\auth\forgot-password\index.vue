<script setup lang="ts">
interface FormRefrence {
	submitForm: () => void
}
const router = useRouter()
const route = useRoute()
const step = ref<number>(1)
const formRef = ref<FormRefrence | null>(null)
const { t } = useI18n()

/** Close login dialog **/
const onCloseLogin = () => {
	const query = { ...route.query }
	delete query.auth
	return router.push({
		path: route.path,
		query,
	})
}

const steps = [
	{},
	{
		component: 'AuthForgotPasswordForm',
		title: t('form.reset-password-title'),
		text: t('form.reset-password-text'),
		btn: t('form.continue'),
	},
	{
		component: 'AuthForgotPasswordOtp',
		btn: t('form.verify'),
	},
	{
		component: 'AuthForgotPasswordReset',
		btn: t('form.change-password'),
	},
]

const onSubmitForm = async () => {
	if (formRef.value) {
		return formRef.value?.submitForm()
	}
}
</script>

<template>
	<Modal
		:dismissible="false"
		:size="!steps[step]?.title?'!pt-0':''"
		:title="steps[step]?.title"
		:description="steps[step]?.text"
		:hide-close="!steps[step]?.title"
		@close="onCloseLogin"
	>
		<template #body>
			<div class="relative col-span-2 flex flex-col w-full gap-2 px-4">
				<component
					:is="steps[step]?.component"
					ref="formRef"
					@set:step="step = $event"
					@close:modal="onCloseLogin"
				/>
			</div>
		</template>
		<template #footer>
			<div class="flex w-full flex-col gap-2 py-4">
				<div class="flex flex-col gap-4">
					<Button
						class="w-full"
						@click.prevent="onSubmitForm"
					>
						{{ $t('form.continue') }}
					</Button>
				</div>
			</div>
		</template>
	</Modal>
</template>
