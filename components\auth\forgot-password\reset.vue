<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import type { AuthResetPassPayload } from '~/interfaces/auth/form'
import { useAuthStore } from '~/store/useAuthStore.client'

const authStore = useAuthStore()
const { t } = useI18n()
const router = useRouter()

const emit = defineEmits<{
	(event: 'close:modal'): void
	(event: 'set:step', value: number): void
}>()

const newEye = ref(false)
const confirmEye = ref(false)
const { locale } = useI18n()
const isRtl = computed(() => locale.value === 'ar')
const userId = computed<number>(() => {
	if (authStore.forgotForm?.userData?.userId) {
		return authStore.forgotForm.userData.userId
	}
	return 0
})

/** Form Body **/
const Form = useForm({
	validationSchema: toTypedSchema(z.object({
		password: z.string()
			.min(7, t('error.required'))
			.regex(/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/, {
				message: t('error.password-strength'),
			}),

		passwordConfirmation: z.string().min(7, t('error.required')),
		userId: z.number(),
	})
		.refine(data => data.password === data.passwordConfirmation, {
			path: ['passwordConfirmation'],
			message: t('error.passwords-not-match'),
		})),

	initialValues: {
		password: '',
		passwordConfirmation: '',
		userId: userId.value,
	},
})

interface Rules {
	'rule-1'?: boolean
	'rule-2'?: boolean
	'rule-3'?: boolean
	'rule-4'?: boolean
}

const passwordRules = computed<Rules | undefined>(() => {
	if (!Form.values) {
		return
	}
	const value = Form.values?.password as string

	return {
		'rule-1': /[a-z]/.test(value) && /[A-Z]/.test(value),
		'rule-2': value.length >= 8,
		'rule-3': /\d/.test(value),
		'rule-4': /[^a-zA-Z0-9]/.test(value),
	}
})

const changePassword = Form.handleSubmit(async (values: AuthResetPassPayload): Promise<void> => {
	return authStore.resetPassword(values).then(() => {
		toast.success(t('form.password-changed-success'))
		nextTick(() => {
			router.push(`/?auth=login`)
		})
	})
})

defineExpose({
	submitForm: changePassword,
})
</script>

<template>
	<form class="flex flex-col gap-4 py-4 min-h-96">
		<div class="flex w-full justify-end">
			<button
				class="p-1 hover:bg-gray-200 rounded-lg flex items-center justify-center"
				@click="emit('set:step', 1)"
			>
				<Icon
					name="lucide:chevron-right"
					:class="{ 'rotate-180': isRtl }"
					size="30px"
				/>
			</button>
		</div>

		<FormField
			v-slot="{ componentField }"
			name="password"
		>
			<FormItem class="w-full relative ">
				<FormLabel class="font-bold">
					{{ $t('form.new-password') }}*
				</FormLabel>
				<FormControl>
					<Input
						:type="!newEye?'password':'text'"
						:placeholder="$t('form.new-password')"
						v-bind="componentField"
					/>
				</FormControl>
				<FormMessage />
				<div class="flex absolute top-7 end-4">
					<button
						class="p-1"
						@click="() => newEye = !newEye"
					>
						<Icon
							:name="newEye?'lucide:eye':'lucide:eye-off'"
							size="18px"
						/>
					</button>
				</div>
			</FormItem>
		</FormField>

		<div class="flex flex-col gap-3 text-sm">
			<span class="font-semibold">
				{{ $t('form.password-hint-title') }}
			</span>

			<ul class="text-sm text-gray-500">
				<li class="flex items-center gap-2">
					<Icon
						v-if="!passwordRules?.['rule-1']"
						name="lucide:circle-check-big"
					/>
					<Icon
						v-else
						class="text-green-600"
						name="lucide:x-circle"
					/>
					<span>{{ $t('password.rule-1') }}</span>
				</li>
				<li class="flex items-center gap-2">
					<Icon
						v-if="!passwordRules?.['rule-2']"
						name="lucide:circle-check-big"
					/>
					<Icon
						v-else
						class="text-green-600"
						name="lucide:x-circle"
					/>
					<span>
						{{ $t('password.rule-2') }}
					</span>
				</li>
				<li class="flex items-center gap-2">
					<Icon
						v-if="!passwordRules?.['rule-3']"
						name="lucide:circle-check-big"
					/>
					<Icon
						v-else
						class="text-green-600"
						name="lucide:x-circle"
					/>
					<span>{{ $t('password.rule-3') }}</span>
				</li>
				<li class="flex items-center gap-2">
					<Icon
						v-if="!passwordRules?.['rule-4']"
						name="lucide:circle-check-big"
					/>
					<Icon
						v-else
						class="text-green-600"
						name="lucide:x-circle"
					/>
					<span>{{ $t('password.rule-4') }}</span>
				</li>
			</ul>
		</div>

		<FormField
			v-slot="{ componentField }"
			name="passwordConfirmation"
		>
			<FormItem class="relative">
				<FormLabel class="font-bold">
					{{ $t('form.confirm-new-password') }}*
				</FormLabel>
				<FormControl>
					<Input
						:type="!confirmEye?'password':'text'"
						:placeholder="$t('form.confirm-new-password')"
						v-bind="componentField"
					/>
				</FormControl>
				<FormMessage />
				<div class="flex absolute top-7 end-4">
					<button
						class="p-1"
						@click="() => confirmEye = !confirmEye"
					>
						<Icon
							:name="confirmEye?'lucide:eye':'lucide:eye-off'"
							size="18px"
						/>
					</button>
				</div>
			</FormItem>
		</FormField>
	</form>
</template>
