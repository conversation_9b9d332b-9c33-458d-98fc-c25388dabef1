<script setup lang="ts">
import { useCurrency } from '~/composables/useCurrency'
import type { Items } from '~/interfaces/cart/cart-list'
import { useCartStore } from '~/store/useCartStore'

const { isOpen = true } = defineProps<{
	isOpen?: boolean
}>()

const emit = defineEmits<{
	(event: 'set:cart-list', value: boolean): void
}>()

const { priceFormat } = useCurrency()
const cartStore = useCartStore()
const list = computed(() => (cartStore?.list || []) as Items[])
const listLength = computed(() => list.value?.length)

const { locale, t: $t } = useI18n()
const direction = computed(() => locale.value !== 'ar' ? 'right' : 'left')

onMounted(() => {
	cartStore.fetchMounted()
})
</script>

<template>
	<Drawer
		id="cart-list"
		:open="isOpen"
		:direction="direction"
		:dismissible="false"
	>
		<DrawerContent
			aria-describedby="Cart Shopping list"
			:class="`drawer-${locale}`"
			class="max-w-lg w-full h-dvh max-sm:rounded-none"
		>
			<DrawerHeader class="border-b border-gray-200 flex justify-between">
				<DrawerTitle class="text-start text-xl font-bold">
					{{
						$t('cart-list.title', {
							item: $t('wish-list.item', { count: listLength }), number: listLength,
						})
					}}
				</DrawerTitle>
				<DrawerDescription class="hidden">
					drawer description
				</DrawerDescription>

				<button
					class="w-8 h-8 rounded-full justify-center flex items-center border border-gray-400 mx-2"
					@click="() => emit('set:cart-list', false)"
				>
					<Icon
						name="lucide:x"
						class="cursor-pointer w-5 h-5 text-gray-700"
					/>
				</button>
			</DrawerHeader>
			<DrawerCartListItems
				@add:cart-list="() => emit('set:cart-list', false)"
			/>
			<DrawerFooter
				v-if="!!listLength"
				class="flex-1 w-full flex-col gap-3 border-t pt-3"
			>
				<div class="flex w-full justify-between items-center">
					<span class="font-bold text-sm">{{ $t('orders.total') }}</span>
					<span class="text-sm font-bold text-gray-600">{{ priceFormat(cartStore.total.value) }}</span>
				</div>
				<div class="flex w-full justify-between items-center">
					<Button
						:size="'sm'"
						variant="text"
						@click.prevent="() => emit('set:cart-list', false)"
					>
						{{ $t('cart-list.continue-shopping') }}
					</Button>

					<Button
						:as-child="true"
						:size="'sm'"
					>
						<NuxtLinkLocale to="/cart">
							{{ $t('wallet.pay-now') }}
						</NuxtLinkLocale>
					</Button>
				</div>
			</DrawerFooter>
		</DrawerContent>
	</Drawer>
</template>

<style scoped lang="scss">
:global(.drawer-en) {
  left: auto !important;
  right: 0 !important;
}

:global(.drawer-ar) {
  left: 0 !important;
  right: auto !important;
}
</style>
