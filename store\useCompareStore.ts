import { defineStore } from 'pinia'
import { useStorage } from '@vueuse/core'

export const useCompareStore = defineStore('compare', {
	state: () => ({
		products: useStorage<number[]>('CompareProducts', []),
	}),

	actions: {
		setProduct(varianceId: number): boolean {
			if (this.isDisableCompare || this.products.includes(varianceId)) {
				return false
			}

			this.products.push(varianceId)
			return true
		},

		removeProduct(varianceId: number) {
			this.products = this.products.filter(id => id !== varianceId)
		},

		hasVariance(varianceId: number): boolean {
			return this.products.indexOf(varianceId) !== -1
		},

		clearAll(): void {
			this.products = []
		},

		fetchProducts(): void {
			this.products = useStorage<number[]>('CompareProducts', [])
		},
	},

	getters: {
		isDisableCompare: state => state.products?.length >= 3,
		isEmpty: state => !state.products?.length,
		count: state => state.products?.length,
	},
})

if (import.meta.hot) {
	import.meta.hot.accept(acceptHMRUpdate(useCompareStore, import.meta.hot))
}
