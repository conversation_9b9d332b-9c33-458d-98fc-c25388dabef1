import { useCategoriesStore } from '~/store/categoriesStore'
import { useCartStore } from '~/store/useCartStore'
import { useFavoriteStore } from '~/store/useFavoriteStore'

export default defineNuxtPlugin({
	name: 'boot',
	enforce: 'pre', // or 'post'
	parallel: true, // or false
	dependsOn: ['api'], // or ['plugin-name']
	async setup(_nuxtApp) {
		const { setVisitorId } = useVisitorId()
		setVisitorId()
	},
	hooks: {

		async 'i18n:localeSwitched'() {
			const categoryStore = useCategoriesStore()
			await categoryStore.fetchMounted()

			const cartStore = useCartStore()
			await cartStore.fetchMounted()

			const favoriteStore = useFavoriteStore()
			await favoriteStore.fetchMounted()
		},
	},
	env: {
		// Set this value to `false` if you don't want the plugin to run when rendering server-only or island components.
		islands: true,
	},
})
