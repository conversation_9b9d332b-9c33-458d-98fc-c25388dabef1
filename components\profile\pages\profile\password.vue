<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import type { AuthPassPayload } from '~/interfaces/auth/form'
import { useAuthStore } from '~/store/useAuthStore.client'

const authStore = useAuthStore()
const { t } = useI18n()

const currentEye = ref(false)
const newEye = ref(false)
const confirmEye = ref(false)
const isSaving = ref<boolean>(false)

/** Form Body **/
const Form = useForm({
	validationSchema: toTypedSchema(z.object({
		oldPassword: z.string().min(7, t('error.required')),

		password: z.string()
			.min(7, t('error.required'))
			.regex(/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/, {
				message: t('error.password-strength'),
			}),

		passwordConfirmation: z.string().min(7, t('error.required')),
	})
		.refine(data => data.password === data.passwordConfirmation, {
			path: ['passwordConfirmation'],
			message: t('error.passwords-not-match'),
		})),

	initialValues: {
		oldPassword: '',
		password: '',
		passwordConfirmation: '',
	},
})

const passwordRules = computed(() => {
	if (!Form.values) {
		return
	}
	const value = Form.values?.password

	return {
		'rule-1': /[a-z]/.test(value) && /[A-Z]/.test(value),
		'rule-2': value.length >= 8,
		'rule-3': /\d/.test(value),
		'rule-4': /[^a-zA-Z0-9]/.test(value),
	}
})

const emit = defineEmits<{
	(event: 'close:modal'): void
}>()

const changePassword = Form.handleSubmit(async (values: AuthPassPayload) => {
	isSaving.value = true
	return authStore.changePassword(values)
		.then(() => {
			toast.success(t('form.password-changed-success'))
			nextTick(() => emit('close:modal'))
		})
		.finally(() => {
			nextTick(() => isSaving.value = false)
		})
})
</script>

<template>
	<Modal
		:title="$t('form.change-password-title')"
		:dismissible="true"
		@close="emit('close:modal')"
	>
		<template #body>
			<div
				role="form"
				class="flex flex-col gap-4 px-4"
			>
				<FormField
					v-slot="{ componentField }"
					name="oldPassword"
				>
					<FormItem class="relative">
						<FormLabel class="font-bold">
							{{ $t('form.current-password') }}*
						</FormLabel>
						<FormControl>
							<Input
								:type="!currentEye?'password':'text'"
								:placeholder="$t('form.current-password')"
								autocomplete="current-password"
								v-bind="componentField"
							/>
						</FormControl>
						<FormMessage />
						<div class="flex absolute top-7 end-4">
							<button
								class="p-1"
								@click="() => currentEye = !currentEye"
							>
								<Icon
									:name="currentEye?'lucide:eye':'lucide:eye-off'"
									size="18px"
								/>
							</button>
						</div>
					</FormItem>
				</FormField>

				<FormField
					v-slot="{ componentField }"
					name="password"
				>
					<FormItem class="w-full relative ">
						<FormLabel class="font-bold">
							{{ $t('form.new-password') }}*
						</FormLabel>
						<FormControl>
							<Input
								v-model="Form.values.password"
								:type="!newEye?'password':'text'"
								autocomplete="new-password"
								:placeholder="$t('form.new-password')"
								v-bind="componentField"
							/>
						</FormControl>
						<FormMessage />
						<div class="flex absolute top-7 end-4">
							<button
								class="p-1"
								@click="() => newEye = !newEye"
							>
								<Icon
									:name="newEye?'lucide:eye':'lucide:eye-off'"
									size="18px"
								/>
							</button>
						</div>
					</FormItem>
				</FormField>

				<div class="flex flex-col gap-3 text-sm">
					<span class="font-semibold">
						{{ $t('form.password-hint-title') }}
					</span>

					<ul class="text-sm text-gray-500">
						<li class="flex items-center gap-2">
							<Icon
								:class="{ 'text-green-600': passwordRules['rule-1'] }"
								:name="!passwordRules['rule-1']?'lucide:x-circle':'lucide:circle-check-big'"
							/>
							<span>{{ $t('password.rule-1') }}</span>
						</li>
						<li class="flex items-center gap-2">
							<Icon
								:class="{ 'text-green-600': passwordRules['rule-2'] }"
								:name="!passwordRules['rule-2']?'lucide:x-circle':'lucide:circle-check-big'"
							/>
							<span>{{ $t('password.rule-2') }}</span>
						</li>
						<li class="flex items-center gap-2">
							<Icon
								:class="{ 'text-green-600': passwordRules['rule-3'] }"
								:name="!passwordRules['rule-3']?'lucide:x-circle':'lucide:circle-check-big'"
							/>
							<span>{{ $t('password.rule-3') }}</span>
						</li>
						<li class="flex items-center gap-2">
							<Icon
								:class="{ 'text-green-600': passwordRules['rule-4'] }"
								:name="!passwordRules['rule-4']?'lucide:x-circle':'lucide:circle-check-big'"
							/>
							<span>{{ $t('password.rule-4') }}</span>
						</li>
					</ul>
				</div>

				<FormField
					v-slot="{ componentField }"
					name="passwordConfirmation"
				>
					<FormItem class="relative">
						<FormLabel class="font-bold">
							{{ $t('form.confirm-new-password') }}*
						</FormLabel>
						<FormControl>
							<Input
								v-model="Form.values.passwordConfirmation"
								autocomplete="new-password"
								:type="!confirmEye?'password':'text'"
								:placeholder="$t('form.confirm-new-password')"
								v-bind="componentField"
							/>
						</FormControl>
						<FormMessage />
						<div class="flex absolute top-7 end-4">
							<button
								class="p-1"
								@click="() => confirmEye = !confirmEye"
							>
								<Icon
									:name="confirmEye?'lucide:eye':'lucide:eye-off'"
									size="18px"
								/>
							</button>
						</div>
					</FormItem>
				</FormField>
			</div>
		</template>

		<template #footer>
			<div class="flex w-full mt-2">
				<Button
					class="w-full"
					:disabled="Form.isSubmitting.value"
					:loading="isSaving"
					@click="changePassword"
				>
					{{ $t('form.change-password-title') }}
				</Button>
			</div>
		</template>
	</Modal>
</template>

<style scoped lang="scss">

</style>
