<script setup lang="ts">
import { useNuxtApp } from 'nuxt/app'
import { toast } from 'vue-sonner'
import type { Items, Total } from '~/interfaces/cart/cart-list'
import { useCartStore } from '~/store/useCartStore'

const { priceFormat } = useCurrency()

const cartStore = useCartStore()
const list = ref<Items[]>([])
const total = ref<Total | null>(null)
const listLength = computed<number>(() => list.value?.length)
const loading = computed<boolean>(() => cartStore.fetching && !list.value?.length)
const subAmount = computed<string>(() => priceFormat(total.value?.value ?? 0) ?? '')
const hasProducts = computed(() => !!listLength.value)

watch(() => cartStore, (value) => {
	list.value = [...value.list] as Items[]
	total.value = value.total as Total
}, { deep: true, immediate: true })

const paymentFeatures = [
	{
		title: 'cart.privacy-payment-title',
		text: 'cart.privacy-payment-text',
		icon: 'ui:privacy-payment',
	},

	{
		title: 'cart.insurance-payment-title',
		text: 'cart.insurance-payment-text',
		icon: 'ui:insurance-payment',
	},

	{
		title: 'cart.secure-payment-title',
		text: 'cart.secure-payment-text',
		icon: 'ui:secure-payment',
	},
]

interface DataResponse {
	orderId: number
}

/** implement pay now **/
const onPayNow = async () => {
	const { $api } = useNuxtApp()
	await $api<DataResponse>('/orders/store', {
		method: 'POST',
	})
		.then((data) => {
			navigateTo(`checkout/${data.orderId}/1`)
		})
		.catch((error) => {
			console.error(error)
			toast.error(error?.message || 'Something went wrong')
		})
}
</script>

<template>
	<Card class="w-2/6 max-sm:w-full">
		<CardHeader>
			<template v-if="loading">
				<Skeleton class="w-full h-10" />
			</template>
			<template v-else>
				<span class="text-xl font-bold text-gray-600">
					{{ $t('cart.details-title') }}
				</span>
			</template>
		</CardHeader>

		<CardContent class="px-0">
			<template v-if="loading">
				<div class="flex flex-col px-6 gap-2">
					<Skeleton class="w-full h-5" />
					<Skeleton class="w-full h-5" />
				</div>
			</template>
			<template v-else>
				<div class="flex w-full flex-col gap-4 px-6">
					<div class="flex w-full justify-between gap-1 border border-gray-200 rounded-md">
						<input
							type="number"
							:placeholder="$t('cart.coupon-placeholder')"
							class="border-none w-full bg-white no-spinner outline-none p-2 rounded-lg text-base"
						>

						<Button
							variant="text"
							class="sm"
						>
							<span>{{ $t('cart.coupon-submit-title') }}</span>
						</Button>
					</div>
				</div>
			</template>

			<div class="flex w-full border-dashed border border-gray-100 my-4" />

			<template v-if="loading">
				<div class="flex flex-col px-6 gap-2">
					<Skeleton class="w-full h-5" />
					<Skeleton class="w-full h-5" />
				</div>
			</template>
			<template v-else>
				<div class="flex w-full flex-col gap-4 px-6">
					<div class="flex justify-between">
						<span class="text-base font-normal text-gray-600">
							{{
								$t('cart.payment-sub-amount-title', {
									item: $t('cart.item', { count: listLength }),
									number: listLength,
								})
							}}
						</span>

						<span class="text-base font-normal text-gray-600">
							{{ subAmount }}
						</span>
					</div>

					<div class="flex justify-between">
						<span class="text-base font-normal text-gray-600">
							{{ $t('cart.payment-tax-inclusive') }}
						</span>
						<span />
					</div>
				</div>
			</template>

			<div class="flex w-full border-dashed border border-gray-100 my-4" />

			<template v-if="loading">
				<div class="flex flex-col px-6 gap-2">
					<Skeleton class="w-full h-6" />
					<Skeleton class="w-full h-6 mb-4" />
					<Skeleton class="w-full h-7" />
					<Skeleton class="w-full h-7 mb-4" />
					<Skeleton class="w-full h-9" />
					<Skeleton class="w-full h-9" />
					<Skeleton class="w-full h-9" />
				</div>
			</template>
			<template v-else>
				<div class="flex w-full flex-col gap-4 px-6">
					<div class="flex justify-between">
						<span class="text-base font-bold text-gray-600">
							{{
								$t('cart.total-title')
							}}
						</span>

						<span class="text-base font-bold text-gray-700">
							{{ subAmount }}
						</span>
					</div>
					<div class="flex mb-2">
						<span class="text-base font-normal text-gray-600">
							{{ $t('cart.payment-delivery-note') }}
						</span>
					</div>
					<Button
						v-if="hasProducts"
						@click.prevent="onPayNow"
					>
						<span>{{ $t('cart-list.confirm-continue-pay') }}</span>
					</Button>

					<Button
						:as-child="true"
						variant="outline"
					>
						<NuxtLinkLocale to="/">
							<span>{{ $t('cart-list.confirm-continue') }}</span>
						</NuxtLinkLocale>
					</Button>

					<template
						v-for="item in paymentFeatures"
						:key="item.icon"
					>
						<div class="flex w-full border rounded border-gray-200 py-3 px-4 items-center gap-4 shadow">
							<div class="flex rounded-full p-2 w-10 h-10 bg-primary-300 justify-center items-center">
								<Icon
									:name="`${item.icon}`"
									size="20"
								/>
							</div>

							<div class="flex flex-col flex-grow">
								<span class="text-sm text-primary-600 font-semibold">{{ $t(item.title) }}</span>
								<span class="text-sm font-normal text-gray-500">{{ $t(item.text) }}</span>
							</div>
						</div>
					</template>
				</div>
			</template>
		</CardContent>
	</Card>
</template>

<style scoped lang="scss">

</style>
