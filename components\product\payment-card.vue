<script setup lang="ts">
import PriceCard from '~/components/product/info/price-card.vue'
import type { UseProductDetails } from '~/interfaces/product/details'
import { useCompareStore } from '~/store/useCompareStore'
import { useFavoriteStore } from '~/store/useFavoriteStore'
import { useCartStore } from '~/store/useCartStore'

interface Props {
	product?: UseProductDetails
	loading?: boolean
	isPaying?: boolean
}

interface Payload {
	productId?: number
	quantity?: number
	varianceId?: number
}

const favoriteStore = useFavoriteStore()
const cartStore = useCartStore()
const compareStore = useCompareStore()

const { product, loading } = defineProps<Props>()
const emit = defineEmits<{
	(event: 'confirm:add-to-cart', value: Payload): void
	(event: 'product:pay-now', value: number): void
	(event: 'product:compare', value: number): void
}>()

const { hasStock, productId, variance } = product
const isFav = ref(false)
const isCompare = ref(false)
const isAddingToCart = ref(false)

const quantity = ref(1)
const hasCart = computed(() => cartStore.hasCartProduct(productId, variance.varianceId))

/** Increase the quantity */
const onIncrease = () => {
	if (quantity.value < product.variance?.stock?.maxPerUser) {
		quantity.value += 1
	}
}

/** Decrease the quantity */
const onDecrease = () => {
	if (quantity.value > 1) {
		quantity.value = quantity.value - 1
	}
}

/** handle on adding item to favorite **/
const onFavorite = async () => {
	if (!isFav.value) {
		return favoriteStore.addToList(product?.productId)
	}

	return favoriteStore.removeFromList(product?.productId)
}

watch(() => favoriteStore.list, () => {
	isFav.value = favoriteStore.hasFav(product?.productId)
}, { immediate: true, deep: true })

const onCart = async () => {
	isAddingToCart.value = true
	await cartStore.addToList({
		productId: product.productId,
		quantity: quantity.value,
		varianceId: product.variance.varianceId,
	})
	emit('confirm:add-to-cart', product)

	nextTick(() => {
		isAddingToCart.value = false
	})
}

watch(() => compareStore.products, () => {
	isCompare.value = compareStore.hasVariance(product?.variance?.varianceId)
}, { immediate: true, deep: true })

const onCompare = async () => {
	emit('product:compare', product?.variance?.varianceId)
}
</script>

<template>
	<div class="flex flex-col w-1/2 gap-4 max-md:w-full">
		<Card class="flex flex-col shadow-md p-4 max-sm:p-0 max-sm:border-0 max-sm:mt-4">
			<price-card
				class="md:flex sm:hidden xs:flex"
				:loading="loading"
				:product="product"
			/>

			<!-- quantity -->
			<div
				v-if="loading"
				class="flex flex-col py-4 gap-2"
			>
				<Skeleton class="w-full h-6" />
				<Skeleton class="w-full h-6" />
			</div>
			<div
				v-else-if="hasStock"
				class="max-sm:hidden flex items-center py-6 gap-6"
			>
				<span>
					{{ $t('product.quantity') }}:
				</span>

				<div
					id="price-section"
					class="grid grid-cols-[auto_1fr_auto] border border-gray-200 rounded max-w-44 items-center overflow-hidden"
				>
					<button
						class="border-e border-gray-200 w-7 items-center flex justify-center h-7"
						@click.prevent="onIncrease"
					>
						<Icon
							name="lucide:plus"
							height="12px"
							width="12px"
						/>
					</button>
					<input
						v-model="quantity"
						name="quantity"
						type="number"
						readonly
						class="no-spinner border-none outline-none col-span-1 w-auto max-w-10 text-center text-xs"
						:max="2"
					>
					<button
						class="border-s border-gray-200 w-7 h-full items-center flex justify-center"
						@click.prevent="onDecrease"
					>
						<Icon
							name="lucide:minus"
							width="12px"
						/>
					</button>
				</div>

				<!--				<span class="text-sm font-medium text-orange-400"> -->
				<!--					{{ $t('product.quantity-will-finished') }} -->
				<!--				</span> -->
			</div>
			<!-- /quantity -->

			<!-- actions -->
			<div
				v-if="loading"
				class="gap-4 flex-col flex"
			>
				<Skeleton class="w-full h-7" />
				<Skeleton class="w-full h-7 bg-primary-200" />
				<Skeleton class="w-full h-7" />
				<Skeleton class="w-full h-7" />
			</div>
			<template v-else>
				<div class="flex flex-col w-full gap-6 max-sm:hidden">
					<template v-if="hasStock">
						<Button
							class="w-full"
							:loading="isPaying"
							@click.prevent="emit('product:pay-now', quantity)"
						>
							{{ $t('product.pay-now') }}
						</Button>

						<Button
							variant="outline"
							class="w-full"
							:class="{ hidden: hasCart }"
							:size="'lg'"
							:loading="isAddingToCart"
							@click.once="onCart"
						>
							{{ $t('product.add-to-cart') }}
						</Button>
					</template>
					<template v-else>
						<Button class="w-full mt-4">
							{{ $t('product.let-me-know') }}
						</Button>
					</template>

					<div class="flex justify-center items-center gap-2 ">
						<Button
							variant="white"
							class="flex items-center"
							@click="onFavorite"
						>
							<div class="flex items-center justify-center">
								<Icon
									v-if="!isFav"
									name="ui:heart"
									class="mx-2"
								/>
								<Icon
									v-if="isFav"
									class="text-primary-600 mx-2"
									name="ui:heart-fill"
								/>

								<span>{{ $t('product.add-favorite') }}</span>
							</div>
						</Button>

						<Button
							variant="white"
							:class="{ 'text-primary-600 bg-primary-100': isCompare }"
							@click.prevent="onCompare"
						>
							<div class="flex items-center justify-center">
								<Icon
									name="ui:compare"
									class="mx-2"
								/>
								<span v-if="isCompare">
									{{ $t('product.remove-comparison') }}
								</span>
								<span v-else>
									{{ $t('product.add-comparison') }}
								</span>
							</div>
						</Button>
					</div>
				</div>

				<div class="flex border-t border-dashed border-gray-200 w-full my-4" />

				<div class="flex p-2 bg-gray-100 text-md font-md rounded items-center mb-4">
					<Icon
						name="lucide:banknote"
						width="22px"
						class="text-gray-500"
					/>

					<div class="col-span-1 flex flex-wrap items-center justify-start">
						<span class="px-2 text-xs">{{ $t('product.payment-title') }}</span>
						<template
							v-for="paymentMethod in product.paymentMethods"
							:key="`product-footer-payment-${paymentMethod.paymentMethodId}`"
						>
							<NuxtImg
								:src="paymentMethod?.media?.logo?.src"
								class="h-5 me-0.5"
								:title="paymentMethod.name"
								:alt="paymentMethod.name"
								provider="backend"
								loading="eager"
								width="20"
								height="20"
								format="webp"
							/>
						</template>
					</div>
				</div>

				<div class="flex p-2 bg-gray-100 text-md font-md rounded items-center">
					<Icon
						name="lucide:truck"
						size="20px"
						class="text-gray-500"
					/>

					<span class="px-2 text-xs">{{ $t('product.free-delivery') }}</span>
				</div>
			</template>
			<!-- /actions -->
		</Card>
	</div>
</template>

<style scoped lang="scss">

</style>
