<script setup lang="ts">
import type { UseProductDetails } from '~/interfaces/product/details'
import { useCartStore } from '~/store/useCartStore'

interface Props {
	product?: UseProductDetails
	loading?: boolean
	isPaying?: boolean
}

interface Payload {
	productId?: number
	quantity?: number
	varianceId?: number
}

const { product } = defineProps<Props>()

const emit = defineEmits<{
	(event: 'confirm:add-to-cart', value: Payload): void
	(event: 'product:pay-now', value: number): void
}>()

const {
	hasStock,
	discountPercent,
	discountAmount,
	priceFormatted,
	offerPriceFormatted,
	discountAmountFormatted,
	productId,
} = product

const cartStore = useCartStore()

const quantity = ref(1)
const stickyRef = ref(null)
const isSticky = ref(false)

const hasCart = computed(() => cartStore.hasCart(productId))

/** Increase the quantity */
const onIncrease = () => {
	if (quantity.value < product.variance?.stock?.maxPerUser) {
		quantity.value += 1
	}
}

/** Decrease the quantity */
const onDecrease = () => {
	if (quantity.value > 1) {
		quantity.value = quantity.value - 1
	}
}

/** Set sticky on scroll down */
useInfiniteScroll(stickyRef, async (entity) => {
	isSticky.value = entity.boundingClientRect.top < 0
}, '0px', false)

const onCart = async () => {
	await cartStore.addToList({
		productId: product.productId,
		quantity: quantity.value,
		varianceId: product.variance.varianceId,
	})

	emit('confirm:add-to-cart', product)
}
</script>

<template>
	<div
		ref="stickyRef"
		class="sm:hidden w-full relative"
	>
		<div
			class="flex flex-col gap-4 w-full transition-all duration-300"
			:class="[isSticky ? 'fixed bottom-0 left-0 w-full bg-white z-50 px-4 drop-shadow-2xl pb-8' : 'hidden']"
		>
			<Card class="flex flex-col shadow-md p-4 max-sm:p-0 max-sm:border-0 max-sm:mt-4">
				<template v-if="!hasStock">
					<div class="flex w-full flex-col gap-2">
						<div class="flex w-full justify-between items-center">
							<div class="text-2xl font-bold">
								{{ $t('product.out-stock') }}
							</div>

							<Icon
								name="ui:out-stock"
								size="25px"
							/>
						</div>

						<div class="flex items-center gap-2">
							<Icon name="ui:bell-ringing" />
							<span class="text-sm font-normal">
								{{ $t('product.let-me-know-when-available') }}
							</span>
						</div>
					</div>
				</template>
				<template v-else>
					<!-- quantity -->

					<div class="grid grid-cols-[1rf_auto] gap-2 w-full">
						<div :class="[!discountAmount?'cols-span-1':'col-span-2']">
							<span class="text-3xl text-primary-600 font-bold">{{ priceFormatted }}</span>
						</div>
						<div
							v-if="discountAmount"
							class="col-span-1 flex gap-2 w-full"
						>
							<div class="flex flex-col w-full gap-2 items-start text-md">
								<div class="flex gap-4">
									<span class="text-gray-600 line-through">{{ offerPriceFormatted }}</span>

									<span class="bg-orange-400 text-white text-xs font-bold px-4 py-1 rounded-full whitespace-nowrap">
										{{ $t('product.card-discount', { amount: discountPercent }) }}
									</span>
								</div>
								<span class="text-orange-400 font-medium">
									{{ $t('product.discount-amount-title', { amount: discountAmountFormatted }) }}
								</span>
							</div>
						</div>
						<div class="col-span-1 flex items-center justify-end gap-6">
							<div
								id="price-section"
								class="grid grid-cols-[auto_1fr_auto] border border-gray-200 rounded max-w-44 items-center overflow-hidden"
							>
								<button
									class="border-e border-gray-200 w-7 items-center flex justify-center h-7"
									@click.prevent="onIncrease"
								>
									<Icon
										name="lucide:plus"
										height="12px"
										width="12px"
									/>
								</button>
								<input
									v-model="quantity"
									name="quantity"
									type="number"
									readonly
									class="no-spinner border-none outline-none col-span-1 w-auto max-w-10 text-center text-xs"
									:max="2"
								>
								<button
									class="border-s border-gray-200 w-7 h-full items-center flex justify-center"
									@click.prevent="onDecrease"
								>
									<Icon
										name="lucide:minus"
										width="12px"
									/>
								</button>
							</div>
						</div>
					</div>
					<!-- /quantity -->
				</template>

				<!-- actions -->
				<div class="flex flex-col w-full gap-6 mt-4">
					<template v-if="hasStock">
						<div class="flex w-full justify-between gap-2">
							<Button
								class="w-full"
								:loading="isPaying"
								@click.prevent="emit('product:pay-now', quantity)"
							>
								{{ $t('product.pay-now') }}
							</Button>
							<Button
								v-if="!hasCart"
								variant="outline"
								class="w-full"
								@click.once="onCart"
							>
								{{ $t('product.add-to-cart') }}
							</Button>
						</div>
					</template>
					<template v-else>
						<Button class="w-full mt-4">
							{{ $t('product.let-me-know') }}
						</Button>
					</template>
				</div>

			<!-- /actions -->
			</Card>
		</div>
	</div>
</template>
