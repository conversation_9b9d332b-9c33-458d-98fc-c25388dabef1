<script setup lang="ts">
import { useAuthStore } from '~/store/useAuthStore.client'

const route = useRoute()

const authPage = computed<string | null>(() => {
	const authStore = useAuthStore()
	// dont show auth forms for loggedIn user
	if (authStore?.isLoggedIn) {
		return null
	}

	switch (route.query.auth) {
		case 'signup':
			return 'AuthSignup'

		case 'verify-otp':
			return 'AuthVerifyOtp'

		case 'reset-password':
			return 'AuthResetPassword'

		case 'forgot-password':
			return 'AuthForgotPassword'

		case 'login':
			return 'AuthLogin'

		default:
			return null
	}
})
</script>

<template>
	<component
		:is="authPage"
		v-if="authPage"
	/>
</template>

<style scoped lang="scss">

</style>
