export interface UserAuth {
	isUserLoggedIn?: boolean
	user?: User
	accessToken?: string
	token?: Token
}

export interface User {
	userId?: number
	firstName?: string
	lastName?: string
	email?: string
	status?: string
	emailVerifiedAt?: unknown
	birthday?: string | null
	two_factor_recovery_codes?: unknown
	two_factor_confirmed_at?: unknown
	deleted_at?: unknown
	createdAt?: string
	updatedAt?: string
	phone?: Phone
	gender?: unknown
	wallet?: Wallet
	phoneVerifiedAt?: unknown
	numberOfOrders?: number
	fullName?: string
	password?: string
	media?: Media
}

export interface Phone {
	iso?: string
	code?: string
	number?: string
	isValid?: boolean
}

export interface Wallet {
	value?: number
	currency?: string
	symbol?: string
}

export interface Token {
	id?: string
	user_id?: number
	client_id?: number
	name?: string
	revoked?: boolean
	created_at?: string
	updated_at?: string
	expires_at?: string
}

export interface Media {
	avatar?: Avatar
}
export interface Avatar {
	id?: number
	src?: string
	preview?: string
	fileSize?: number
	mimeType?: string
	disk?: string
	sort?: number
	alt?: unknown
}
