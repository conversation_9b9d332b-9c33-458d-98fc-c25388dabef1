<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import type FormPhoneValue from '~/interfaces/form'
import { useAuthStore } from '~/store/useAuthStore.client'

const { t } = useI18n()

const authStore = useAuthStore()
const emit = defineEmits<{
	(event: 'set:step', value: number): void
}>()

/** validate form **/
const Form = useForm({
	validationSchema: toTypedSchema(
		z.object({
			phone: z.object({
				number: z.string().min(5, t('error.required')),
				iso: z.string(),
				code: z.string(),
				isValid: z.boolean(),
			})
				.refine((phone) => {
					if (!phone.isValid)
						Form.setErrors({
							phone: t('error.phone-number-invalid'),
						})
				}),
		}),
	),

	initialValues: toRaw({
		phone: {
			code: '962',
			iso: 'JO',
			number: '',
			isValid: false,
		},
	}),
})

/** Handle on login user **/
const forgotPassword = Form.handleSubmit(async (values) => {
	return authStore.forgotPasswordPhone(values.phone)
		.then((data) => {
			authStore.forgotForm = {
				userData: data,
				phone: { ...values.phone },
			}
			toast.success(t('form.verification-code-sent'))
			emit('set:step', 2)
		})
		.catch((error) => {
			throw error
		})
})

defineExpose({
	submitForm: forgotPassword,
})
</script>

<template>
	<div class="flex flex-col gap-4 pt-6 min-h-32">
		<FormPhone
			:error="Form.errors?.value?.phone"
			@update="(value: FormPhoneValue) => {
				Form.setFieldValue('phone', {
					number: value.nationalNumber,
					code: value.countryCallingCode,
					iso: value.countryCode,
					isValid: value.isValid,
				})
			}"
		/>
	</div>
</template>
